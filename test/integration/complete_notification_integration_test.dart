import 'package:flutter_test/flutter_test.dart';
import 'package:railops/models/onboarding_notification_model.dart';
import 'package:railops/services/notification_services/onboarding_notification_service.dart';
import 'package:railops/services/notification_services/notification_integration_helper.dart';
import 'package:railops/types/train_types/upcoming_station_response.dart';

/// Comprehensive integration test for Phase 1 + Phase 2 notification system
/// This test verifies that both phases are completely implemented and working together
void main() {
  group('Complete Notification System Integration Test', () {
    late OnboardingNotificationService notificationService;

    setUpAll(() {
      notificationService = OnboardingNotificationService();

      // Clear any existing notifications
      notificationService.clearProcessedNotifications();

      // Configure both Phase 1 and Phase 2 settings
      _configureCompleteSystem();
    });

    group('Phase 1 Core Functionality Tests', () {
      test('Phase 1: Basic notification service initialization', () {
        expect(notificationService, isNotNull);
        expect(notificationService.processedNotificationCount, equals(0));
      });

      test('Phase 1: Basic notification types exist', () {
        // Verify Phase 1 notification types
        expect(OnboardingNotificationType.boarding, isNotNull);
        expect(OnboardingNotificationType.offBoarding, isNotNull);
        expect(OnboardingNotificationType.stationApproaching, isNotNull);
        expect(OnboardingNotificationType.coachReminder, isNotNull);
        expect(OnboardingNotificationType.berthReminder, isNotNull);
      });

      test('Phase 1: Basic trigger types exist', () {
        // Verify Phase 1 trigger types
        expect(NotificationTriggerType.immediate, isNotNull);
        expect(NotificationTriggerType.timeBasedDelay, isNotNull);
        expect(NotificationTriggerType.locationBasedProximity, isNotNull);
        expect(NotificationTriggerType.stationSequence, isNotNull);
      });

      test('Phase 1: Basic trigger creation', () {
        final immediateTriger = NotificationTrigger.immediate();
        expect(immediateTriger.type, NotificationTriggerType.immediate);

        final delayTrigger =
            NotificationTrigger.timeDelay(const Duration(minutes: 5));
        expect(delayTrigger.type, NotificationTriggerType.timeBasedDelay);
        expect(delayTrigger.delay, const Duration(minutes: 5));

        final proximityTrigger = NotificationTrigger.proximity(2.5);
        expect(proximityTrigger.type,
            NotificationTriggerType.locationBasedProximity);
        expect(proximityTrigger.proximityThresholdKm, 2.5);

        final stationTrigger = NotificationTrigger.stationsBefore(3);
        expect(stationTrigger.type, NotificationTriggerType.stationSequence);
        expect(stationTrigger.stationsBefore, 3);
      });

      test('Phase 1: Basic notification data creation', () {
        final context = StationNotificationContext(
          stationName: 'Test Station',
          trainNumber: 'TEST123',
          date: '2024-01-01',
          coachNumbers: ['A1', 'B2'],
          allStations: ['Start', 'Test Station', 'End'],
        );

        final notification = OnboardingNotificationData(
          notificationId: 'test_id',
          type: OnboardingNotificationType.boarding,
          title: 'Test Notification',
          body: 'Test Body',
          stationContext: context,
        );

        expect(notification.notificationId, 'test_id');
        expect(notification.type, OnboardingNotificationType.boarding);
        expect(notification.title, 'Test Notification');
        expect(notification.stationContext.stationName, 'Test Station');
      });

      test('Phase 1: Integration helper basic functionality', () async {
        // Test basic configuration
        expect(() {
          NotificationIntegrationHelper.configureNotificationTiming(
            boardingNotificationDelay: const Duration(minutes: 10),
            offBoardingNotificationDelay: const Duration(minutes: 20),
            stationsBeforeAlert: 2,
          );
        }, returnsNormally);

        // Test basic test notification
        expect(() async {
          await NotificationIntegrationHelper.sendTestNotification(
            title: 'Phase 1 Test',
            body: 'Testing Phase 1 functionality',
          );
        }, returnsNormally);
      });
    });

    group('Phase 2 Enhanced Functionality Tests', () {
      test('Phase 2: Enhanced notification types exist', () {
        // Verify Phase 2 notification types
        expect(OnboardingNotificationType.stationApproachAlert, isNotNull);
        expect(OnboardingNotificationType.boardingCountUpdate, isNotNull);
        expect(OnboardingNotificationType.offBoardingPreparation, isNotNull);
        expect(OnboardingNotificationType.trainStatusUpdate, isNotNull);
        expect(OnboardingNotificationType.proximityAlert, isNotNull);
      });

      test('Phase 2: Enhanced trigger types exist', () {
        // Verify Phase 2 trigger types
        expect(NotificationTriggerType.passengerCountThreshold, isNotNull);
        expect(NotificationTriggerType.trainStatusChange, isNotNull);
        expect(NotificationTriggerType.scheduleChange, isNotNull);
      });

      test('Phase 2: Enhanced trigger creation', () {
        final countTrigger = NotificationTrigger.passengerCountThreshold(10);
        expect(
            countTrigger.type, NotificationTriggerType.passengerCountThreshold);
        expect(countTrigger.conditions['passenger_threshold'], 10);

        final statusTrigger = NotificationTrigger.trainStatusChange(
          statusTypes: ['delay', 'cancellation'],
          debounceDelay: const Duration(minutes: 2),
        );
        expect(statusTrigger.type, NotificationTriggerType.trainStatusChange);
        expect(statusTrigger.conditions['status_types'],
            ['delay', 'cancellation']);

        final scheduleTrigger = NotificationTrigger.scheduleChange(
          minimumDelayThreshold: const Duration(minutes: 15),
        );
        expect(scheduleTrigger.type, NotificationTriggerType.scheduleChange);
        expect(scheduleTrigger.conditions['minimum_delay_minutes'], 15);
      });

      test('Phase 2: Enhanced configuration methods', () {
        expect(() {
          NotificationIntegrationHelper.configureProximityNotifications(
            proximityThresholdKm: 3.0,
            locationUpdateInterval: const Duration(minutes: 1),
            enableLocationMonitoring: true,
          );
        }, returnsNormally);

        expect(() {
          NotificationIntegrationHelper.configurePassengerCountNotifications(
            passengerCountThreshold: 8,
            enableBoardingCountUpdates: true,
            enableOffBoardingPreparation: true,
          );
        }, returnsNormally);

        expect(() {
          NotificationIntegrationHelper.configureTrainStatusMonitoring(
            monitoredStatusTypes: ['delay', 'cancellation', 'platform_change'],
            statusCheckInterval: const Duration(minutes: 3),
            enableScheduleChangeAlerts: true,
          );
        }, returnsNormally);
      });

      test('Phase 2: Enhanced test notification methods', () async {
        // Test all Phase 2 notification types
        expect(() async {
          await NotificationIntegrationHelper.sendTestProximityNotification(
            stationName: 'New Delhi',
            distanceKm: 2.5,
          );
        }, returnsNormally);

        expect(() async {
          await NotificationIntegrationHelper.sendTestStationApproachAlert(
            stationName: 'Mumbai Central',
            minutesBeforeArrival: 5,
          );
        }, returnsNormally);

        expect(() async {
          await NotificationIntegrationHelper.sendTestTrainStatusUpdate(
            trainNumber: '12157',
            statusType: 'delay',
            statusMessage: 'Train delayed by 15 minutes',
          );
        }, returnsNormally);

        expect(() async {
          await NotificationIntegrationHelper.sendTestBoardingCountUpdate(
            stationName: 'Pune Junction',
            currentCount: 12,
            previousCount: 8,
            coaches: ['A1', 'B2'],
          );
        }, returnsNormally);
      });
    });

    group('Integration Tests - Phase 1 + Phase 2 Working Together', () {
      test('All notification types are available', () {
        const allTypes = OnboardingNotificationType.values;

        // Phase 1 types
        expect(allTypes.contains(OnboardingNotificationType.boarding), true);
        expect(allTypes.contains(OnboardingNotificationType.offBoarding), true);
        expect(allTypes.contains(OnboardingNotificationType.stationApproaching),
            true);

        // Phase 2 types
        expect(
            allTypes.contains(OnboardingNotificationType.stationApproachAlert),
            true);
        expect(
            allTypes.contains(OnboardingNotificationType.boardingCountUpdate),
            true);
        expect(
            allTypes
                .contains(OnboardingNotificationType.offBoardingPreparation),
            true);
        expect(allTypes.contains(OnboardingNotificationType.trainStatusUpdate),
            true);
        expect(
            allTypes.contains(OnboardingNotificationType.proximityAlert), true);

        // Verify we have all expected types (5 Phase 1 + 5 Phase 2 = 10 total)
        expect(allTypes.length, greaterThanOrEqualTo(10));
      });

      test('All trigger types are available', () {
        const allTriggers = NotificationTriggerType.values;

        // Phase 1 triggers
        expect(allTriggers.contains(NotificationTriggerType.immediate), true);
        expect(
            allTriggers.contains(NotificationTriggerType.timeBasedDelay), true);
        expect(
            allTriggers
                .contains(NotificationTriggerType.locationBasedProximity),
            true);
        expect(allTriggers.contains(NotificationTriggerType.stationSequence),
            true);

        // Phase 2 triggers
        expect(
            allTriggers
                .contains(NotificationTriggerType.passengerCountThreshold),
            true);
        expect(allTriggers.contains(NotificationTriggerType.trainStatusChange),
            true);
        expect(
            allTriggers.contains(NotificationTriggerType.scheduleChange), true);

        // Verify we have all expected triggers (4 Phase 1 + 3 Phase 2 = 7 total)
        expect(allTriggers.length, greaterThanOrEqualTo(7));
      });

      test('Configuration system works for both phases', () {
        final stats = NotificationIntegrationHelper.getNotificationStats();

        // Should have configuration data
        expect(stats, isNotNull);
        expect(stats, isA<Map<String, dynamic>>());
        expect(stats.containsKey('processed_notifications'), true);
      });

      test(
          'Backward compatibility - Phase 1 still works with Phase 2 installed',
          () async {
        // Test that Phase 1 functionality still works
        await NotificationIntegrationHelper.sendTestNotification(
          title: 'Backward Compatibility Test',
          body: 'Phase 1 should still work',
        );

        // Verify notification was processed
        expect(notificationService.processedNotificationCount, greaterThan(0));
      });
    });
  });
}

/// Configure the complete notification system for testing
void _configureCompleteSystem() {
  // Phase 1 configuration
  NotificationIntegrationHelper.configureNotificationTiming(
    boardingNotificationDelay: const Duration(minutes: 15),
    offBoardingNotificationDelay: const Duration(minutes: 30),
    stationsBeforeAlert: 2,
  );

  // Phase 2 configuration
  NotificationIntegrationHelper.configureProximityNotifications(
    proximityThresholdKm: 5.0,
    locationUpdateInterval: const Duration(minutes: 2),
    enableLocationMonitoring: true,
  );

  NotificationIntegrationHelper.configurePassengerCountNotifications(
    passengerCountThreshold: 10,
    enableBoardingCountUpdates: true,
    enableOffBoardingPreparation: true,
  );

  NotificationIntegrationHelper.configureTrainStatusMonitoring(
    monitoredStatusTypes: ['delay', 'cancellation', 'platform_change'],
    statusCheckInterval: const Duration(minutes: 5),
    enableScheduleChangeAlerts: true,
  );
}
