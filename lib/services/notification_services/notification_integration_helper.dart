import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:railops/services/train_services/upcoming_station_service.dart';
import 'package:railops/types/train_types/upcoming_station_response.dart';
import 'package:railops/services/authentication_services/jwt_service.dart';
import 'package:railops/services/firebase_cloud_function_service.dart';
import 'package:railops/services/fcm_token_service.dart';

/// Helper class for Firebase Cloud Function notification integration
/// This provides a simple interface for testing and using the /notify endpoint
class NotificationIntegrationHelper {
  /// Get real user context for testing (replaces hardcoded test values)
  static Future<Map<String, dynamic>> getRealUserContext() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Get user ID from JWT or SharedPreferences
      String? userId = await JwtService.getStoredUserId();
      userId ??= prefs.getString('user_id');

      // Get train number from SharedPreferences
      final trainNumber = prefs.getString('trainNo') ?? '';

      // Get auth token
      final authToken = prefs.getString('authToken') ?? '';

      // Get current date
      final currentDate = FirebaseCloudFunctionService.getCurrentDateString();

      // Get real GPS coordinates
      Position? position;
      try {
        // Check location permissions
        LocationPermission permission = await Geolocator.checkPermission();
        if (permission == LocationPermission.denied) {
          permission = await Geolocator.requestPermission();
        }

        if (permission == LocationPermission.whileInUse ||
            permission == LocationPermission.always) {
          position = await Geolocator.getCurrentPosition(
            locationSettings: const LocationSettings(
              accuracy: LocationAccuracy.medium,
            ),
          );
        }
      } catch (e) {
        if (kDebugMode) {
          print('❌ Error getting GPS location: $e');
        }
      }

      return {
        'user_id': userId,
        'train_number': trainNumber,
        'auth_token': authToken,
        'current_date': currentDate,
        'latitude':
            position?.latitude.toString() ?? '28.6139', // Fallback to New Delhi
        'longitude': position?.longitude.toString() ??
            '77.2090', // Fallback to New Delhi
        'has_real_location': position != null,
        'has_user_context':
            (userId?.isNotEmpty ?? false) && trainNumber.isNotEmpty,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting real user context: $e');
      }
      return {
        'user_id': null,
        'train_number': '',
        'auth_token': '',
        'current_date': FirebaseCloudFunctionService.getCurrentDateString(),
        'latitude': '28.6139', // Fallback to New Delhi
        'longitude': '77.2090', // Fallback to New Delhi
        'has_real_location': false,
        'has_user_context': false,
        'error': e.toString(),
      };
    }
  }

  /// Test real notification pipeline (replaces mock test notifications)
  static Future<Map<String, dynamic>> testRealNotificationPipeline({
    String? customTrainNumber,
    String? customLat,
    String? customLng,
  }) async {
    try {
      if (kDebugMode) {
        print('🧪 Testing REAL notification pipeline...');
      }

      // Get real user context
      final userContext = await getRealUserContext();

      if (!userContext['has_user_context']) {
        return {
          'success': false,
          'error': 'Missing user context',
          'details':
              'User ID or train number not found. Please ensure you are logged in and have a train assignment.',
          'user_context': userContext,
        };
      }

      final userId = userContext['user_id'] as String;
      final trainNumber =
          customTrainNumber ?? userContext['train_number'] as String;
      final lat = customLat ?? userContext['latitude'] as String;
      final lng = customLng ?? userContext['longitude'] as String;
      final currentDate = userContext['current_date'] as String;

      if (kDebugMode) {
        print('📍 Using real coordinates: $lat, $lng');
        print('🚂 Using real train: $trainNumber');
        print('👤 Using real user ID: $userId');
        print('📅 Using date: $currentDate');
      }

      // Get FCM token
      String? fcmToken;
      try {
        fcmToken = await FcmTokenService.getFcmToken();
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ Warning: Could not get FCM token: $e');
        }
      }

      // Call the REAL Firebase Cloud Function /notify endpoint
      final result = await FirebaseCloudFunctionService.callNotifyFunction(
        userId: userId,
        trainNumber: trainNumber,
        date: currentDate,
        lat: lat,
        lng: lng,
        fcmToken: fcmToken,
      );

      if (kDebugMode) {
        print(
            '📥 Cloud Function Result: ${result['status']} - ${result['message']}');
      }

      return {
        'success': result['status'] == 'sent',
        'status': result['status'],
        'message': result['message'],
        'details': result['details'],
        'user_context': userContext,
        'request_data': {
          'user_id': userId,
          'train_number': trainNumber,
          'date': currentDate,
          'coordinates': '$lat, $lng',
          'fcm_token_available': fcmToken != null,
        },
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error testing real notification pipeline: $e');
      }
      return {
        'success': false,
        'error': 'Pipeline test failed',
        'details': e.toString(),
      };
    }
  }
}
